<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.MerchantUserMapper">

    <select id="merchantStaffPageList" resultType="com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO">
        SELECT
        distinct rm.user_id userId,
        fmu.merchant_id merchantId,
        fmu.tenant_id tenantId,
        rm.account account,
        rm.name name,
        rm.status status,
        rm.create_time createTime
        FROM
        frt_merchant_user fmu
        LEFT JOIN frt_account rm ON fmu.user_id = rm.user_id and rm.platform_type = 3
        LEFT JOIN frt_account_bind_role abr ON rm.user_id = abr.user_id
        LEFT JOIN frt_merchant_user_store fms ON fms.user_id = fmu.user_id
        WHERE
        fmu.tanant_id =#{query.tanantId, jdbcType=VARCHAR}
        AND fmu.merchant_id =#{query.merchantId, jdbcType=VARCHAR}
        AND fmu.user_type = "OTHER"
        <if test="query.searchContent != null and query.searchContent != ''">
            AND (mu.account LIKE CONCAT('%', #{query.searchContent, jdbcType=VARCHAR}, '%') or mu.name LIKE CONCAT('%',
            #{query.searchContent, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="query.status != null">
            AND rm.status = #{query.status, jdbcType=INTEGER}
        </if>
        <if test="query.storeId != null">
            AND fms.store_id = #{query.storeId, jdbcType=VARCHAR}
        </if>
        <if test="query.roleId != null">
            AND abr.role_id = #{query.roleId, jdbcType=VARCHAR}
        </if>
        AND fmu.is_del = 0
        AND rm.is_del = 0
        AND abr.is_del = 0
        AND fms.is_del = 0
    </select>

</mapper>
