package com.frt.usercore.service.impl.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.constants.CommonConstant;
import com.frt.usercore.common.constants.RedisPrefixConstant;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.usercore.common.exception.InternalException;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.PasswordUtil;
import com.frt.usercore.config.SysConfig;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.domain.result.common.CheckLoginPasswordResult;
import com.frt.usercore.service.common.PasswordCommonService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@AllArgsConstructor
public class PasswordCommonServiceImpl implements PasswordCommonService {

	private SysConfig sysConfig;
	private AccountDAO accountDAO;
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * 校验登录密码
	 *
	 * @param account      账号
	 * @param password     密码
	 * @param platformType 平台类型 1-运营后台 2-代理商 3-商户
	 * @param tenantId     租户ID
	 * @return 校验结果
	 */
	@Override
	public CheckLoginPasswordResult checkLoginPassword(String account, String password, Integer platformType, String tenantId) {

		// 参数校验
		validateParameters(account, password, platformType, tenantId);

		// 构建缓存键
		String cacheKey = buildCacheKey(tenantId, account, platformType);

		// 获取当前错误次数
		Integer currentErrorCount = getCurrentErrorCount(cacheKey);

		// 获取平台配置
		PlatformConfig platformConfig = getPlatformConfig(platformType);

		// 检查是否已被锁定
		if (currentErrorCount >= platformConfig.getMaxErrorCount()) {
			return handleAccountLocked(currentErrorCount, platformConfig, cacheKey);
		}

		// 校验密码
		AccountDO accountDO = accountDAO.getByAccount(account);
		if (accountDO == null) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "账号不存在");
		}
		boolean passwordValid = accountDO.getPassword().equals(PasswordUtil.encryptPasswordWithSalt(password, accountDO.getSalt()));

		if (passwordValid) {
			// 密码正确，清除错误次数缓存
			clearErrorCount(cacheKey);
			return buildSuccessResult();
		} else {
			// 密码错误，增加错误次数
			return handlePasswordError(cacheKey, currentErrorCount, platformConfig);
		}
	}

	/**
	 * 重置登录错误次数
	 *
	 * @param account      账号
	 * @param platformType 平台类型
	 * @param tenantId     租户ID
	 */
	@Override
	public void resetLoginErrorCount(String account, Integer platformType, String tenantId) {

		// 参数校验（不需要密码参数）
		validateResetParameters(account, platformType, tenantId);

		// 构建缓存键
		String cacheKey = buildCacheKey(tenantId, account, platformType);

		// 清除错误次数缓存
		clearErrorCount(cacheKey);

	}

	/**
	 * 参数校验
	 *
	 * @param account      账号
	 * @param password     密码
	 * @param platformType 平台类型
	 * @param tenantId     租户ID
	 */
	private void validateParameters(String account, String password, Integer platformType, String tenantId) {
		if (StrUtil.isBlank(account)) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 账号不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "账号不能为空");
		}
		if (StrUtil.isBlank(password)) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 密码不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "密码不能为空");
		}
		if (null == platformType) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 平台类型不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "平台类型不能为空");
		}
		if (StrUtil.isBlank(tenantId)) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 租户ID不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "租户ID不能为空");
		}

		// 验证平台类型是否有效
		try {
			PlatformEnum platformEnum = PlatformEnum.getByCode(platformType);
			if (platformEnum == null) {
				LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 无效的平台类型 >> platformType = {}", platformType);
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
						"PARAM_ERROR", "无效的平台类型");
			}
		} catch (NumberFormatException e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateParameters >> 平台类型格式错误 >> platformType = {}", platformType);
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "平台类型格式错误");
		}
	}

	/**
	 * 重置参数校验（不包含密码）
	 *
	 * @param account      账号
	 * @param platformType 平台类型
	 * @param tenantId     租户ID
	 */
	private void validateResetParameters(String account, Integer platformType, String tenantId) {
		if (StrUtil.isBlank(account)) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateResetParameters >> 账号不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "账号不能为空");
		}
		if (null == platformType) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateResetParameters >> 平台类型不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "平台类型不能为空");
		}
		if (StrUtil.isBlank(tenantId)) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateResetParameters >> 租户ID不能为空");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "租户ID不能为空");
		}

		// 验证平台类型是否有效
		try {
			PlatformEnum platformEnum = PlatformEnum.getByCode(platformType);
			if (platformEnum == null) {
				LogUtil.error(log, "PasswordCommonServiceImpl.validateResetParameters >> 无效的平台类型 >> platformType = {}", platformType);
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
						"PARAM_ERROR", "无效的平台类型");
			}
		} catch (NumberFormatException e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.validateResetParameters >> 平台类型格式错误 >> platformType = {}", platformType);
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "平台类型格式错误");
		}
	}

	/**
	 * 构建缓存键
	 *
	 * @param tenantId     租户ID
	 * @param account      账号
	 * @param platformType 平台类型
	 * @return 缓存键
	 */
	private String buildCacheKey(String tenantId, String account, Integer platformType) {
		return StrUtil.format(RedisPrefixConstant.LOGIN_PASSWORD_ERROR_COUNT, tenantId, account, platformType);
	}

	/**
	 * 获取当前错误次数
	 *
	 * @param cacheKey 缓存键
	 * @return 当前错误次数
	 */
	private Integer getCurrentErrorCount(String cacheKey) {
		try {
			String errorCountStr = stringRedisTemplate.opsForValue().get(cacheKey);
			return StrUtil.isBlank(errorCountStr) ? CommonConstant.INTEGER_ZERO : Integer.parseInt(errorCountStr);
		} catch (Exception e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.getCurrentErrorCount >> 获取错误次数异常 >> cacheKey = {}, error = {}",
					cacheKey, e.getMessage(), e);
			return CommonConstant.INTEGER_ZERO;
		}
	}

	/**
	 * 获取平台配置
	 *
	 * @param platformType 平台类型
	 * @return 平台配置
	 */
	private PlatformConfig getPlatformConfig(Integer platformType) {
		PlatformEnum platformEnum = PlatformEnum.getByCode(platformType);
		if (platformEnum == null) {
			LogUtil.error(log, "PasswordCommonServiceImpl.getPlatformConfig >> 无效的平台类型 >> platformType = {}", platformType);
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
					"PARAM_ERROR", "无效的平台类型");
		}
		PlatformConfig config = new PlatformConfig();

		switch (platformEnum) {
			case OPERATION:
				config.setMaxErrorCount(sysConfig.getOperationMaxErrorCount());
				config.setLockTime(calculateOperationLockTime());
				config.setShowLockTime(sysConfig.getOperationShowLockTime());
				break;
			case MERCHANT:
				config.setMaxErrorCount(sysConfig.getMerchantMaxErrorCount());
				config.setLockTime(sysConfig.getMerchantLockTime());
				config.setShowLockTime(sysConfig.getMerchantShowLockTime());
				break;
			case AGENT:
				// 代理商暂时使用商户的配置
				config.setMaxErrorCount(sysConfig.getMerchantMaxErrorCount());
				config.setLockTime(sysConfig.getMerchantLockTime());
				config.setShowLockTime(sysConfig.getMerchantShowLockTime());
				break;
			default:
				LogUtil.error(log, "PasswordCommonServiceImpl.getPlatformConfig >> 不支持的平台类型 >> platformType = {}", platformType);
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(),
						"PARAM_ERROR", "不支持的平台类型");
		}

		return config;
	}

	/**
	 * 计算运营后台锁定时间（到当天截止时间的分钟数）
	 *
	 * @return 锁定时间（分钟）
	 */
	private Integer calculateOperationLockTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX);
		long minutesUntilEndOfDay = java.time.Duration.between(now, endOfDay).toMinutes();
		return (int) minutesUntilEndOfDay;
	}

	/**
	 * 处理账号已锁定的情况
	 *
	 * @param currentErrorCount 当前错误次数
	 * @param platformConfig    平台配置
	 * @param cacheKey          缓存键
	 * @return 校验结果
	 */
	private CheckLoginPasswordResult handleAccountLocked(Integer currentErrorCount, PlatformConfig platformConfig, String cacheKey) {
		LogUtil.info(log, "PasswordCommonServiceImpl.handleAccountLocked >> 账号已被锁定 >> currentErrorCount = {}, maxErrorCount = {}",
				currentErrorCount, platformConfig.getMaxErrorCount());

		CheckLoginPasswordResult result = new CheckLoginPasswordResult();
		result.setSuccess(false);
		result.setPasswordErrorCount(currentErrorCount);
		result.setPasswordMaxErrorCount(platformConfig.getMaxErrorCount());
		result.setActualLockTime(platformConfig.getLockTime());
		result.setShowLockTime(platformConfig.getShowLockTime());

		// 更新缓存过期时间
		updateCacheExpiration(cacheKey, platformConfig.getLockTime());

		LogUtil.info(log, "PasswordCommonServiceImpl.handleAccountLocked >> 返回锁定结果 >> result = {}", JSON.toJSONString(result));
		return result;
	}

	/**
	 * 处理密码错误的情况
	 *
	 * @param cacheKey          缓存键
	 * @param currentErrorCount 当前错误次数
	 * @param platformConfig    平台配置
	 * @return 校验结果
	 */
	private CheckLoginPasswordResult handlePasswordError(String cacheKey, Integer currentErrorCount, PlatformConfig platformConfig) {
		// 增加错误次数
		Integer newErrorCount = currentErrorCount + CommonConstant.INTEGER_ONE;
		LogUtil.info(log, "PasswordCommonServiceImpl.handlePasswordError >> 密码错误，增加错误次数 >> newErrorCount = {}", newErrorCount);

		// 更新缓存
		updateErrorCount(cacheKey, newErrorCount, platformConfig.getLockTime());

		CheckLoginPasswordResult result = new CheckLoginPasswordResult();
		result.setSuccess(false);
		result.setPasswordErrorCount(newErrorCount);
		result.setPasswordMaxErrorCount(platformConfig.getMaxErrorCount());

		// 如果达到最大错误次数，设置锁定时间
		if (newErrorCount >= platformConfig.getMaxErrorCount()) {
			result.setActualLockTime(platformConfig.getLockTime());
			result.setShowLockTime(platformConfig.getShowLockTime());
			LogUtil.info(log, "PasswordCommonServiceImpl.handlePasswordError >> 达到最大错误次数，账号被锁定");
		}

		LogUtil.info(log, "PasswordCommonServiceImpl.handlePasswordError >> 返回密码错误结果 >> result = {}", JSON.toJSONString(result));
		return result;
	}

	/**
	 * 构建成功结果
	 *
	 * @return 校验结果
	 */
	private CheckLoginPasswordResult buildSuccessResult() {
		CheckLoginPasswordResult result = new CheckLoginPasswordResult();
		result.setSuccess(true);
		result.setPasswordErrorCount(CommonConstant.INTEGER_ZERO);
		result.setPasswordMaxErrorCount(CommonConstant.INTEGER_ZERO);
		result.setActualLockTime(CommonConstant.INTEGER_ZERO);
		result.setShowLockTime(CommonConstant.INTEGER_ZERO);

		LogUtil.info(log, "PasswordCommonServiceImpl.buildSuccessResult >> 返回成功结果 >> result = {}", JSON.toJSONString(result));
		return result;
	}

	/**
	 * 更新错误次数缓存
	 *
	 * @param cacheKey   缓存键
	 * @param errorCount 错误次数
	 * @param lockTime   锁定时间（分钟）
	 */
	private void updateErrorCount(String cacheKey, Integer errorCount, Integer lockTime) {
		try {
			stringRedisTemplate.opsForValue().set(cacheKey, String.valueOf(errorCount), lockTime, TimeUnit.MINUTES);
			LogUtil.info(log, "PasswordCommonServiceImpl.updateErrorCount >> 更新错误次数缓存成功 >> cacheKey = {}, errorCount = {}, lockTime = {}",
					cacheKey, errorCount, lockTime);
		} catch (Exception e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.updateErrorCount >> 更新错误次数缓存异常 >> cacheKey = {}, error = {}",
					cacheKey, e.getMessage(), e);
		}
	}

	/**
	 * 更新缓存过期时间
	 *
	 * @param cacheKey 缓存键
	 * @param lockTime 锁定时间（分钟）
	 */
	private void updateCacheExpiration(String cacheKey, Integer lockTime) {
		try {
			stringRedisTemplate.expire(cacheKey, lockTime, TimeUnit.MINUTES);
			LogUtil.info(log, "PasswordCommonServiceImpl.updateCacheExpiration >> 更新缓存过期时间成功 >> cacheKey = {}, lockTime = {}",
					cacheKey, lockTime);
		} catch (Exception e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.updateCacheExpiration >> 更新缓存过期时间异常 >> cacheKey = {}, error = {}",
					cacheKey, e.getMessage(), e);
		}
	}

	/**
	 * 清除错误次数缓存
	 *
	 * @param cacheKey 缓存键
	 */
	private void clearErrorCount(String cacheKey) {
		try {
			stringRedisTemplate.delete(cacheKey);
		} catch (Exception e) {
			LogUtil.error(log, "PasswordCommonServiceImpl.clearErrorCount >> 清除错误次数缓存异常 >> cacheKey = {}, error = {}",
					cacheKey, e.getMessage(), e);
		}
	}

	/**
	 * 平台配置内部类
	 */
	@Data
	private static class PlatformConfig {
		/**
		 * 最大错误次数
		 */
		private Integer maxErrorCount;

		/**
		 * 锁定时间（分钟）
		 */
		private Integer lockTime;

		/**
		 * 展示锁定时间（分钟）
		 */
		private Integer showLockTime;
	}
}
