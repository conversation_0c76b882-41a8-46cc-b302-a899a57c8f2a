package com.frt.usercore.service.impl;

import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.usercore.domain.result.operationadmin.menumanager.MenuListQueryResult;
import com.frt.usercore.service.OperationAdminMenuManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运营后台菜单管理服务实现类
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerServiceImpl.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationAdminMenuManagerServiceImpl implements OperationAdminMenuManagerService {


    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @Override
    public List<MenuListQueryResult> getMenuList(MenuListQueryParam param) {
        LogUtil.info(log, "OperationAdminMenuManagerServiceImpl.getMenuList >> 接口开始 >> param = {}", JSON.toJSONString(param));
        
        // TODO: 实现权限列表查询逻辑
        
        LogUtil.info(log, "OperationAdminMenuManagerServiceImpl.getMenuList >> 接口结束");
        return null;
    }
}
