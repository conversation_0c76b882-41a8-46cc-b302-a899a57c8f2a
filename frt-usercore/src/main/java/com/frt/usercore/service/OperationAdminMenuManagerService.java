package com.frt.usercore.service;

import com.frt.usercore.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.usercore.domain.result.operationadmin.menumanager.MenuListQueryResult;

import java.util.List;

/**
 * 运营后台菜单管理服务接口
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerService.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
public interface OperationAdminMenuManagerService {

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    List<MenuListQueryResult> getMenuList(MenuListQueryParam param);
}
