/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.enums.business.AccountStatusEnum;
import com.frt.usercore.common.enums.business.BusinessIdEnum;
import com.frt.usercore.common.enums.business.EmployeeTypeEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.business.UserTypeEnum;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.*;
import com.frt.usercore.dao.repository.*;
import com.frt.usercore.domain.dto.param.MerchantStaffListQueryParamDTO;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.mapper.MerchantUserMapper;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.result.MerchantUserAccountCheckResult;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.UserStoreListResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;
import com.frt.usercore.service.MerchantUserManagerService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version MerchantUserManagerServiceImpl.java, v 0.1 2025-08-27 17:58 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantUserManagerServiceImpl implements MerchantUserManagerService {

    private final MerchantUserDAO merchantUserDAO;

    private final MerchantDAO merchantDAO;

    private final AccountDAO accountDAO;

    private final TenantRoleDAO tenantRoleDAO;

    private final AccountBindRoleDAO accountBindRoleDAO;

    private final MerchantUserStoreDAO merchantUserStoreDAO;

    private final MerchantStoreInfoDAO merchantStoreInfoDAO;

    private final StoreManagerMapper storeManagerMapper;
    private final MerchantUserMapper merchantUserMapper;

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @Override
    public PageResult<UserInfo> getUserList(PageParam<UserListQueryParam> param) {
        PageParam<MerchantStaffListQueryParamDTO> pageDTO = new PageParam<>(param.getPage(), param.getPageSize());
        // 查询条件组装
        UserListQueryParam queryParam = param.getQuery();
        MerchantStaffListQueryParamDTO queryDTO = new MerchantStaffListQueryParamDTO();
        queryDTO.setTenantId(TenantContextUtil.getTenantId());
        queryDTO.setMerchantId(queryParam.getMerchantId());
        queryDTO.setSearchContent(queryParam.getSearchContent());
        queryDTO.setRoleId(queryParam.getRoleId());
        queryDTO.setStoreId(queryParam.getStoreId());
        queryDTO.setStatus(queryParam.getStatus());
        pageDTO.setQuery(queryDTO);
        // 查询列表
        Page<MerchantStaffInfoResultDTO> pageList = merchantUserDAO.merchantStaffPageList(pageDTO);
        // 列表参数处理
        List<MerchantStaffInfoResultDTO> pageListRecords = pageList.getRecords();
        if (CollectionUtil.isEmpty(pageListRecords)) {
            return PageResult.init();
        }
        List<String> userIdList = pageListRecords.stream().map(MerchantStaffInfoResultDTO::getUserId).toList();
        // 根据 userId 查询角色名称
        List<FindRoleByUserIdListResultDTO> userRoleList = tenantRoleDAO.findRoleByUserIdList(userIdList);
        // 使用 stream 转换为 map
        Map<String, String> userRoleMap = userRoleList.stream().collect(Collectors.toMap(FindRoleByUserIdListResultDTO::getUserId, FindRoleByUserIdListResultDTO::getRoleName));
        PageResult<UserInfo> pageResult = new PageResult<>();
        pageResult.setTotal(pageList.getTotal());
        pageResult.setSize(pageList.getSize());
        pageResult.setCurrent(pageList.getCurrent());

        List<UserInfo> resultList = pageListRecords.stream().map(item -> {
            UserInfo userInfo = merchantUserMapper.toMerchantStaffInfoDTO(item);
            // 所属角色
            userInfo.setRoleName(userRoleMap.getOrDefault(item.getUserId(), StringPool.EMPTY));
            // 创建时间处理
            userInfo.setCreateTime(DateUtil.formatDateTime(item.getCreateTime()));
            // 最后登录时间处理（时间戳）
            userInfo.setLastLoginTime(DateUtil.secondToTime(item.getLastLoginTime()));
            // 查询关联门店
            List<MerchantUserStoreDO> merchantUserStoreList = merchantUserStoreDAO.findByUserIdAndMerchantId(item.getUserId(), item.getMerchantId());
            // 门店名称使用逗号分割
            if (CollectionUtil.isNotEmpty(merchantUserStoreList)) {
                List<MerchantStoreInfoDO> merchantStoreInfoList = merchantStoreInfoDAO.findByStoreIdListAndMerchantId(merchantUserStoreList.stream().map(MerchantUserStoreDO::getStoreId).toList(), item.getMerchantId());
                String storeName = merchantStoreInfoList.stream().map(MerchantStoreInfoDO::getStoreName).collect(Collectors.joining(","));
                userInfo.setStoreName(storeName);
            } else {
                userInfo.setStoreName(StringPool.EMPTY);
            }
            return userInfo;
        }).collect(Collectors.toList());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @Override
    public UserDetailQueryResult getUserDetail(UserDetailQueryParam param) {
        log.info("MerchantUserManagerServiceImpl.getUserDetail >> 获取员工详情开始 >> param = {}", JSON.toJSONString(param));
        ValidateUtil.validate(param);
        final String merchantId = TenantContextUtil.getMerchantId();
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(merchantId, param.getUserId());
        if (merchantUserDO == null) {
            throw ValidateUtil.validateMsg("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            throw ValidateUtil.validateMsg("员工不存在");
        }
        final UserDetailQueryResult result = new UserDetailQueryResult();
        final List<MerchantUserStoreDO> storeDOList = merchantUserStoreDAO.findByUserIdAndMerchantId(param.getUserId(), merchantId);
        if (CollectionUtil.isNotEmpty(storeDOList)) {
            final List<String> storeIdList = storeDOList.stream().map(MerchantUserStoreDO::getStoreId).distinct().toList();
            final List<MerchantStoreInfoDO> storeInfoList = merchantStoreInfoDAO.findByStoreIdListAndMerchantId(storeIdList, merchantId);
            result.setStoreList(storeInfoList.stream().map(storeManagerMapper::coverMerchantStoreInfoDOToUserStoreResult).toList());
        }
        result.setUserId(accountDO.getUserId());
        result.setAccount(accountDO.getAccount());
        result.setName(accountDO.getName());
        result.setPhone(accountDO.getPhone());
        final AccountBindRoleDO accountBindRoleDO = accountBindRoleDAO.getByUserId(accountDO.getUserId());
        if (accountBindRoleDO != null) {
            result.setRoleId(accountBindRoleDO.getRoleId());
            final TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleId(accountBindRoleDO.getRoleId());
            result.setRoleName(tenantRoleDO.getRoleName());
        }
        log.info("MerchantUserManagerServiceImpl.getUserDetail >> 获取员工详情结束 >> result = {}", JSON.toJSONString(merchantUserDO));
        return result;
    }

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult addUser(UserAddParam param) {
        // 校验账号是否已存在
        AccountDO existingAccount = accountDAO.getByAccount(param.getAccount());
        if (existingAccount != null) {
            return CommonResult.failed("员工账号已存在");
        }

        final TenantRoleDO roleDO = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (roleDO == null) {
            return CommonResult.failed("角色不存在");
        }

        String merchantId = TenantContextUtil.getMerchantId();
        String userId = BusinessIdEnum.MERCHANT_USER_ID.generateId();
        // 创建账户信息
        AccountDO accountDO = new AccountDO();
        accountDO.setUserId(userId);
        accountDO.setAccount(param.getAccount());
        accountDO.setPassword(param.getPassword()); // 实际应用中密码需要加密
        accountDO.setSalt("");
        accountDO.setIsAdmin(0);
        accountDO.setPhone(param.getPhone());
        accountDO.setPlatformId(merchantId);
        accountDO.setPlatformType(PlatformEnum.MERCHANT.getCode());
        accountDO.setName(param.getName());
        accountDO.setAccountStatus(param.getStatus() != null ? ("Y".equals(param.getStatus()) ? 1 : 2) : 1);
        accountDAO.save(accountDO);

        //角色用户绑定关系
        AccountBindRoleDO accountBindRoleDO = new AccountBindRoleDO();
        accountBindRoleDO.setUserId(accountDO.getUserId());
        accountBindRoleDO.setRoleId(param.getRoleId());
        accountBindRoleDAO.save(accountBindRoleDO);

        // 创建商户用户关联信息
        MerchantUserDO merchantUserDO = new MerchantUserDO();
        merchantUserDO.setUserId(userId);
        merchantUserDO.setUsername(param.getAccount());
        // 设置租户和商户ID（需要根据实际情况获取）
        merchantUserDO.setTenantId(TenantContextUtil.getTenantId());
        merchantUserDO.setMerchantId(merchantId);
        merchantUserDO.setUserType(EmployeeTypeEnum.EMPLOYEE.name());
        merchantUserDAO.save(merchantUserDO);

        return CommonResult.success("添加员工成功");
    }

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult updateUser(UserUpdateParam param) {
        ValidateUtil.validate(param);
        // 校验角色是否存在（如果提供了角色ID）
        TenantRoleDO role = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (role == null) {
            return CommonResult.failed("角色不存在");
        }

        // 更新商户用户信息
        MerchantUserDO merchantUserDO = merchantUserDAO.getByUserId(param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }

        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccount(accountDO.getAccount());
        updateDO.setPassword(accountDO.getPassword());
        updateDO.setName(param.getName());
        updateDO.setPhone(param.getPhone());
        accountDAO.updateByUserId(updateDO);

        AccountBindRoleDO accountBindRoleDO = new AccountBindRoleDO();
        accountBindRoleDO.setUserId(accountDO.getUserId());
        accountBindRoleDO.setRoleId(role.getRoleId());
        accountBindRoleDAO.updateRoleIdByUserId(accountBindRoleDO);


        return CommonResult.success("更新员工成功");
    }

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult deleteUser(UserDeleteParam param) {
        ValidateUtil.validate(param);
        String merchantId = TenantContextUtil.getMerchantId();
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(merchantId, param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccountStatus(AccountStatusEnum.CANCELLED.getCode());
        accountDAO.updateByUserId(updateDO);
        return CommonResult.success("删除员工成功");
    }

    /**
     * 禁用员工
     *
     * @param param 禁用员工参数
     * @return 操作结果
     */
    @Override
    public CommonResult disableAndEnableUser(UserDisableAndEnableParam param) {
        ValidateUtil.validate(param);
        String merchantId = TenantContextUtil.getMerchantId();
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(merchantId, param.getUserId());
        if (merchantUserDO == null) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            return CommonResult.failed("员工不存在");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setAccountStatus(AccountStatusEnum.NORMAL.getCode().equals(accountDO.getAccountStatus()) ? AccountStatusEnum.DISABLED.getCode() : AccountStatusEnum.NORMAL.getCode());
        accountDAO.updateByUserId(updateDO);
        return CommonResult.success("更新员工成功");
    }

    /**
     * 检查员工账号
     *
     * @param param
     * @return
     */
    @Override
    public MerchantUserAccountCheckResult checkUserAccount(MerchantUserAccountCheckParam param) {
        ValidateUtil.validate(param);
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        //获取商户ID
        String merchantId = TenantContextUtil.getMerchantId();
        final AccountDO accountDO = accountDAO.getByAccountAndTenantIdAndMerchantIdAndPlatformType(param.getAccount(), tenantId, merchantId, platformEnum.getCode());
        final MerchantUserAccountCheckResult result = new MerchantUserAccountCheckResult();
        result.setSuccess(ObjectUtil.isNull(accountDO));
        return result;
    }

    /**
     * 修改员工密码
     *
     * @param param
     */
    @Override
    public void updatePassword(MerchantUserUpdatePasswordParam param) {
        String merchantId = TenantContextUtil.getMerchantId();
        final MerchantUserDO merchantUserDO = merchantUserDAO.getByMerchantIdAndUserId(merchantId, param.getUserId());
        if (merchantUserDO == null) {
            throw ValidateUtil.validateMsg("员工不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO == null || AccountStatusEnum.CANCELLED.getCode().equals(accountDO.getAccountStatus())) {
            throw ValidateUtil.validateMsg("员工不存在");
        }
        if (!param.getPassword().equals(param.getSecondPassword())) {
            throw ValidateUtil.validateMsg("两次输入的密码不一致");
        }
        final AccountDO updateDO = new AccountDO();
        updateDO.setUserId(accountDO.getUserId());
        updateDO.setPassword(param.getPassword());
        accountDAO.updateByUserId(updateDO);
    }

    @Override
    public UserStoreListResult queryUserStoreList(UserStoreListParam param) {

        String loginUserId = null;
        UserTypeEnum userType = TenantContextUtil.getUserType();
        if (UserTypeEnum.STAFF.equals(userType)) {
            loginUserId = TenantContextUtil.getUserId();
        }
        String merchantId = TenantContextUtil.getMerchantId();

        // 获取登录用户下单门店列表
        List<MerchantUserStoreDO> userStoreDOList = merchantUserStoreDAO.findByUserIdAndMerchantIdAndStoreId(loginUserId, merchantId, param.getStoreId());
        if (CollectionUtil.isEmpty(userStoreDOList)) {
            UserStoreListResult result = new UserStoreListResult();
            result.setList(Lists.newArrayList());
            return result;
        }
        List<String> storeIdList = userStoreDOList.stream().map(MerchantUserStoreDO::getStoreId).distinct().toList();

        // 根据筛选条件获取门店信息
        List<MerchantStoreInfoDO> storeInfoDOList = merchantStoreInfoDAO.findByStoreIdListAndMerchantIdAndStoreName(storeIdList, merchantId,  param.getStoreName());
        if (CollectionUtil.isEmpty(storeInfoDOList)) {
            UserStoreListResult result = new UserStoreListResult();
            result.setList(Lists.newArrayList());
            return result;
        }

        List<UserStoreListResult.UserStoreListItemResult> resultList = Lists.newArrayList();
        for (MerchantStoreInfoDO item : storeInfoDOList) {
            UserStoreListResult.UserStoreListItemResult result = storeManagerMapper.coverMerchantStoreInfoDOToUserStoreListItemResult(item);
            resultList.add(result);
        }

        UserStoreListResult result = new UserStoreListResult();
        result.setList(resultList);
        return result;
    }
}