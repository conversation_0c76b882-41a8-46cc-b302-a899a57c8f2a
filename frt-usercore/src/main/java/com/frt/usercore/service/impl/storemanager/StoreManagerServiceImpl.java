package com.frt.usercore.service.impl.storemanager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.enums.business.BusinessIdEnum;
import com.frt.usercore.common.enums.business.UserTypeEnum;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.entity.MerchantDO;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.entity.MerchantUserStoreDO;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import com.frt.usercore.dao.repository.MerchantDAO;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.dao.repository.MerchantUserStoreDAO;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.storemanager.StoreManagerService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class StoreManagerServiceImpl implements StoreManagerService {

    private MerchantDAO merchantDAO;
    private GaodeCodeDAO gaodeCodeDAO;
    private UnityCategoryDAO unityCategoryDAO;
    private MerchantStoreInfoDAO merchantStoreInfoDAO;
    private MerchantUserStoreDAO merchantUserStoreDAO;
    private StoreManagerMapper storeManagerMapper;
    private final TransactionTemplate transactionTemplate;


    @Override
    public PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param) {
        StoreListQueryParam query = param.getQuery();
        ValidateUtil.validate(query);

        // 如果登录的是商户则不适用userId查询所有商户数据，如果登录人是员工，则只能查看自己门店数据
        String loginUserId = null;
        if (UserTypeEnum.STAFF.equals(TenantContextUtil.getUserType())) {
            loginUserId = TenantContextUtil.getUserId();
        }
        String loginMerchantId = TenantContextUtil.getMerchantId();

        // 获取登录用户的门店ID列表
        List<MerchantUserStoreDO> userStoreDOList = merchantUserStoreDAO.findByUserIdAndMerchantIdAndStoreId(loginUserId, loginMerchantId, query.getStoreId());
        if (CollectionUtil.isEmpty(userStoreDOList)) {
            PageResult<StoreListQueryResult> pageResult = new PageResult<>();
            pageResult.setTotal(0L);
            pageResult.setCurrent(0L);
            pageResult.setSize((long) param.getPageSize());
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }
        List<String> storeIdList = userStoreDOList.stream().map(MerchantUserStoreDO::getStoreId).distinct().toList();
        query.setStoreIdList(storeIdList);

        Page<MerchantStoreInfoDO> pageDO = merchantStoreInfoDAO.findStorePageWithTenantId(param);
        if (CollectionUtil.isEmpty(pageDO.getRecords())) {
            PageResult<StoreListQueryResult> pageResult = new PageResult<>();
            pageResult.setTotal(pageDO.getTotal());
            pageResult.setSize(pageDO.getSize());
            pageResult.setCurrent(pageDO.getCurrent());
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }

        List<StoreListQueryResult> resultList = new ArrayList<>();
        List<MerchantStoreInfoDO> records = pageDO.getRecords();
        for (MerchantStoreInfoDO item : records) {
            StoreListQueryResult result = storeManagerMapper.coverMerchantStoreInfoDOToStoreListQueryResult(item);
            resultList.add(result);
        }
        PageResult<StoreListQueryResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageDO.getTotal());
        pageResult.setSize(pageDO.getSize());
        pageResult.setCurrent(pageDO.getCurrent());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    @Override
    public StoreInfoAddResult addStoreInfo(StoreInfoAddParam param) {

        ValidateUtil.validate(param);
        String merchantId = TenantContextUtil.getMerchantId();

        // 参数校验
        MerchantStoreInfoDO storeName = merchantStoreInfoDAO.getInfoByStoreName(param.getStoreName());
        if (null != storeName) {
            throw ValidateUtil.validateMsg("门店名称已存在");
        }
        MerchantDO merchantInfo = merchantDAO.getInfoByMerchantIdWithTenantId(merchantId);
        if (null == merchantInfo) {
            throw ValidateUtil.validateMsg("商户信息不存在");
        }
        if (StrUtil.equalsIgnoreCase("SUCCESS", merchantInfo.getMerchantStatus())) {
            throw ValidateUtil.validateMsg("商户状态异常");
        }
        UnityCategoryDO unityCategoryDO = unityCategoryDAO.getInfoById(param.getUnityCatId());
        if (null == unityCategoryDO) {
            throw ValidateUtil.validateMsg("类目信息不存在");
        }
        GaodeCodeDO provinceCode = gaodeCodeDAO.getInfoByCode(param.getProvinceCode());
        if (null == provinceCode) {
            throw ValidateUtil.validateMsg("省信息不存在");
        }
        GaodeCodeDO cityCode = gaodeCodeDAO.getInfoByCode(param.getCityCode());
        if (null == cityCode) {
            throw ValidateUtil.validateMsg("市信息不存在");
        }
        GaodeCodeDO countyCode = gaodeCodeDAO.getInfoByCode(param.getCountyCode());
        if (null == countyCode) {
            throw ValidateUtil.validateMsg("县信息不存在");
        }

        String loginUserId = TenantContextUtil.getUserId();
        String loginMerchantId = TenantContextUtil.getMerchantId();
        String storeId = BusinessIdEnum.MERCHANT_STORE_ID.generateId();
        String tenantId = TenantContextUtil.getTenantId();
        transactionTemplate.execute(status -> {
            // 商户登录信息与门店关系
            MerchantUserStoreDO userStoreDO = new MerchantUserStoreDO();
            userStoreDO.setTenantId(tenantId);
            userStoreDO.setMerchantId(loginMerchantId);
            userStoreDO.setUserId(loginUserId);
            userStoreDO.setStoreId(storeId);
            merchantUserStoreDAO.save(userStoreDO);

            // 商户关系
            MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoAddParamToStoreInfoDO(param);
            storeInfoDO.setTenantId(tenantId);
            storeInfoDO.setMerchantId(loginMerchantId);
            storeInfoDO.setStoreId(storeId);
            merchantStoreInfoDAO.save(storeInfoDO);
            return true;
        });

        StoreInfoAddResult result = new StoreInfoAddResult();
        result.setStoreId(storeId);
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param) {

        String merchantId = TenantContextUtil.getMerchantId();
        // 参数校验
        MerchantStoreInfoDO storeName = merchantStoreInfoDAO.getInfoByStoreName(param.getStoreName());
        if (null != storeName) {
            throw ValidateUtil.validateMsg("门店名称已存在");
        }
        MerchantDO merchantInfo = merchantDAO.getInfoByMerchantIdWithTenantId(merchantId);
        if (null == merchantInfo) {
            throw ValidateUtil.validateMsg("商户信息不存在");
        }
        if (StrUtil.equalsIgnoreCase("SUCCESS", merchantInfo.getMerchantStatus())) {
            throw ValidateUtil.validateMsg("商户状态异常");
        }
        UnityCategoryDO unityCategoryDO = unityCategoryDAO.getInfoById(param.getUnityCatId());
        if (null == unityCategoryDO) {
            throw ValidateUtil.validateMsg("类目信息不存在");
        }
        GaodeCodeDO provinceCode = gaodeCodeDAO.getInfoByCode(param.getProvinceCode());
        if (null == provinceCode) {
            throw ValidateUtil.validateMsg("省信息不存在");
        }
        GaodeCodeDO cityCode = gaodeCodeDAO.getInfoByCode(param.getCityCode());
        if (null == cityCode) {
            throw ValidateUtil.validateMsg("市信息不存在");
        }
        GaodeCodeDO countyCode = gaodeCodeDAO.getInfoByCode(param.getCountyCode());
        if (null == countyCode) {
            throw ValidateUtil.validateMsg("县信息不存在");
        }
        MerchantStoreInfoDO dbStoreInfoDO = merchantStoreInfoDAO.getInfoByStoreIdWithTenantId(param.getStoreId());
        if (null == dbStoreInfoDO) {
            throw ValidateUtil.validateMsg("门店信息不存在");
        }
        // 权限校验
        String loginUserId = null;
        UserTypeEnum userType = TenantContextUtil.getUserType();
        if (UserTypeEnum.STAFF.equals(userType)) {
            loginUserId = TenantContextUtil.getUserId();
        }
        List<MerchantUserStoreDO> merchantUserStoreDOList = merchantUserStoreDAO.findByUserIdAndMerchantIdAndStoreId(loginUserId, TenantContextUtil.getMerchantId(), param.getStoreId());
        if (CollectionUtil.isEmpty(merchantUserStoreDOList)) {
            throw ValidateUtil.validateMsg("当前账号没有修改权限");
        }

        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoDO(param);
        storeInfoDO.setMerchantId(merchantId);
        merchantStoreInfoDAO.updateByStoreIdAndMerchantIdWithTenantId(storeInfoDO);
        StoreInfoUpdateResult result = new StoreInfoUpdateResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param) {
        ValidateUtil.validate(param);
        MerchantStoreInfoDO infoDO = merchantStoreInfoDAO.getInfoByStoreIdWithTenantId(param.getStoreId());
        if (infoDO == null) {
            throw ValidateUtil.validateMsg("门店信息不存在");
        }

        StoreInfoQueryResult result = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoQueryResult(infoDO);
        Integer unityCatId = result.getUnityCatId();
        result.setLevel1CategoryId(0);
        result.setLevel1CategoryName("未知");
        result.setUnityCatId(0);
        result.setUnityCatName("未知");
        if (unityCatId != null) {
            UnityCategoryDO secondCategoryDO = unityCategoryDAO.getInfoById(unityCatId);
            if (null != secondCategoryDO) {
                UnityCategoryDO firstCategoryDO = unityCategoryDAO.getInfoById(secondCategoryDO.getParentId());
                if (null != firstCategoryDO) {
                    result.setLevel1CategoryId(firstCategoryDO.getId());
                    result.setLevel1CategoryName(firstCategoryDO.getCatName());
                    result.setUnityCatId(secondCategoryDO.getId());
                    result.setUnityCatName(secondCategoryDO.getCatName());
                }
            }
        }
        return result;
    }
}
