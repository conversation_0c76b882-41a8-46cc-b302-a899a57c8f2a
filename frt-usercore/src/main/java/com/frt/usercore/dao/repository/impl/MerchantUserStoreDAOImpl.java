package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.MerchantUserStoreDO;
import com.frt.usercore.dao.mapper.MerchantUserStoreMapper;
import com.frt.usercore.dao.repository.MerchantUserStoreDAO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商户员工门店关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantUserStoreDAOImpl extends ServiceImpl<MerchantUserStoreMapper, MerchantUserStoreDO> implements MerchantUserStoreDAO {

    @Override
    public List<MerchantUserStoreDO> findByUserIdAndMerchantId(String userId, String merchantId) {
        return this.lambdaQuery()
                .eq(MerchantUserStoreDO::getUserId, userId)
                .eq(MerchantUserStoreDO::getMerchantId, userId)
                .eq(MerchantUserStoreDO::getIsDel, 0)
                .list();
    }

    @Override
    public List<MerchantUserStoreDO> findByUserIdAndMerchantIdAndStoreId(String userId, String merchantId, String storeId) {
        if (StrUtil.isBlank(userId)
                && StrUtil.isBlank(merchantId)
                && StrUtil.isBlank(storeId)) {
            throw ValidateUtil.validateMsg("参数不能为空");
        }
        return this.lambdaQuery()
                .eq(StrUtil.isNotBlank(userId), MerchantUserStoreDO::getUserId, userId)
                .eq(StrUtil.isNotBlank(merchantId), MerchantUserStoreDO::getMerchantId, merchantId)
                .eq(StrUtil.isNotBlank(storeId), MerchantUserStoreDO::getStoreId, storeId)
                .list();
    }
}
