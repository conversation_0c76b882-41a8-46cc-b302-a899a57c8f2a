package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.mapper.MerchantStoreInfoMapper;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 门店信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class MerchantStoreInfoDAOImpl extends ServiceImpl<MerchantStoreInfoMapper, MerchantStoreInfoDO> implements MerchantStoreInfoDAO {

    @Override
    public List<MerchantStoreInfoDO> findByStoreIdListAndMerchantId(List<String> storeIdList, String merchantId) {
       return this.lambdaQuery()
                .in(MerchantStoreInfoDO::getStoreId, storeIdList)
                .eq(MerchantStoreInfoDO::getMerchantId, merchantId)
                .eq(MerchantStoreInfoDO::getIsDel, 0)
               .list();
    }

    @Override
    public Page<MerchantStoreInfoDO> findStorePageWithTenantId(PageParam<StoreListQueryParam> param) {
        return this.lambdaQuery()
                .eq(MerchantStoreInfoDO::getTenantId, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO::getMerchantId, TenantContextUtil.getMerchantId())
                .likeRight(StrUtil.isNotBlank(param.getQuery().getStoreName()), MerchantStoreInfoDO::getStoreName, param.getQuery().getStoreName())
                .eq(StrUtil.isNotBlank(param.getQuery().getStoreId()), MerchantStoreInfoDO::getStoreId, param.getQuery().getStoreId())
                .eq(StrUtil.isNotBlank(param.getQuery().getIsShow()), MerchantStoreInfoDO::getIsShow, param.getQuery().getIsShow())
                .in(CollectionUtil.isNotEmpty(param.getQuery().getStoreIdList()), MerchantStoreInfoDO::getStoreId, param.getQuery().getStoreIdList())
                .orderByAsc(MerchantStoreInfoDO::getCreateTime)
                .page(Page.of(param.getPage(), param.getPageSize()));
    }

    @Override
    public MerchantStoreInfoDO getInfoByStoreIdWithTenantId(String storeId) {
        return this.lambdaQuery()
                .eq(MerchantStoreInfoDO::getTenantId, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO::getStoreId, storeId)
                .last("limit 1")
                .one();
    }

    @Override
    public MerchantStoreInfoDO getInfoByStoreName(String storeName) {
        return this.lambdaQuery()
                .eq(MerchantStoreInfoDO::getStoreName, storeName)
                .last("limit 1")
                .one();
    }

    @Override
    public void updateByStoreIdAndMerchantIdWithTenantId(MerchantStoreInfoDO storeInfoDO) {
        update()
                .eq(MerchantStoreInfoDO.STORE_ID, storeInfoDO.getStoreId())
                .eq(MerchantStoreInfoDO.TENANT_ID, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO.MERCHANT_ID, storeInfoDO.getMerchantId())
                .update(storeInfoDO);
    }

    @Override
    public List<MerchantStoreInfoDO> findByStoreIdListAndMerchantIdAndStoreName(List<String> storeIdList, String merchantId, String storeName) {
        return this.lambdaQuery()
                .in(CollectionUtil.isNotEmpty(storeIdList), MerchantStoreInfoDO::getStoreId, storeIdList)
                .eq(StrUtil.isNotBlank(merchantId), MerchantStoreInfoDO::getMerchantId, merchantId)
                .eq(StrUtil.isNotBlank(storeName), MerchantStoreInfoDO::getStoreName, storeName)
                .list();
    }
}
