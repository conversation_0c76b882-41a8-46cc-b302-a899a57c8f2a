package com.frt.usercore.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.AccountDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.param.PageParam;

/**
 * <p>
 * 运营后台端用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountDAO extends IService<AccountDO> {

    /**
     * 根据账号和平台类型查询单个账号
     * @param account 账号
     * @param platformType 平台类型
     * @return AccountDO 账号信息
     */
    AccountDO selectByAccountAndPlatformType(String account, Integer platformType,String tenantId);

    /**
     * 根据账号和密码和平台类型查询单个账号
     * @param account
     * @param password
     * @param platformType
     * @return
     */
    AccountDO getByAccountAndPasswordAndPlatformType(String account, String password, Integer platformType);

    /**
     * 根据账号查询账户信息
     * @param account 账号
     * @return 账户信息
     */
    AccountDO getByAccount(String account);

    /**
     * 根据UserId查询账户信息
     * @param userId 账号
     * @return 账户信息
     */
    AccountDO getByUserId(String userId);

    boolean updateByUserId(AccountDO accountDO);

    /**
     * 运营后台员工列表
     *
     * @param pageDTO 分页参数
     * @return 分页结果
     */
	Page<AccountDO> findOperationAdminPageList(PageParam<UserListQueryParamDTO> pageDTO);

    /**
     * 获取商户员工列表
     * @param account
     * @param tenantId
     * @param platformId
     * @param platformType
     * @return
     */
    AccountDO getByAccountAndTenantIdAndMerchantIdAndPlatformType(String account, String tenantId, String platformId, Integer platformType);
}
