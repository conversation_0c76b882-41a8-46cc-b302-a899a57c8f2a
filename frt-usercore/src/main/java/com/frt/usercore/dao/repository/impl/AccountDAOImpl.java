package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.constants.SqlConstant;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.mapper.AccountMapper;
import com.frt.usercore.dao.repository.AccountDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.param.PageParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class AccountDAOImpl extends ServiceImpl<AccountMapper, AccountDO> implements AccountDAO {

    @Override
    public AccountDO selectByAccountAndPlatformType(String account, Integer platformType,  String tenantId) {
        QueryWrapper<AccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId)
                .eq("account", account)
                .eq("platform_type", platformType)
                .eq("is_del", 0);
        return getOne(queryWrapper);
    }
    @Override
    public AccountDO getByAccountAndPasswordAndPlatformType(String account, String password, Integer platformType) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                   .eq(AccountDO::getPassword, password)
                   .eq(AccountDO::getPlatformType, platformType);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据账号查询账户信息
     *
     * @param account 账号
     * @return 账户信息
     */
    @Override
    public AccountDO getByAccount(String account) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                .eq(AccountDO::getIsDel, 0)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 根据UserId查询账户信息
     *
     * @param userId 账号
     * @return 账户信息
     */
    @Override
    public AccountDO getByUserId(String userId) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getUserId, userId)
                .eq(AccountDO::getIsDel, 0)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean updateByUserId(AccountDO accountDO) {
        return this.baseMapper.updateByUserId(accountDO);
    }

    /**
     * 运营后台员工列表
     *
     * @param pageDTO 分页参数
     * @return 分页结果
     */
    @Override
    public Page<AccountDO> findOperationAdminPageList(PageParam<UserListQueryParamDTO> pageDTO) {
        Page<UserListQueryParamDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());

        return getBaseMapper().blocAccountPageList(page, pageDTO.getQuery());
    }

    /**
     * 获取商户员工列表
     *
     * @param account
     * @param tenantId
     * @param platformId
     * @param platformType
     * @return
     */
    @Override
    public AccountDO getByAccountAndTenantIdAndMerchantIdAndPlatformType(String account, String tenantId, String platformId, Integer platformType) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                .eq(AccountDO::getTenantId, tenantId)
                .eq(StrUtil.isNotBlank(platformId), AccountDO::getPlatformId, platformId)
                .eq(AccountDO::getPlatformType, platformType)
                .last(SqlConstant.SQL_LIMIT_ONE);
        return this.getOne(queryWrapper);
    }
}
