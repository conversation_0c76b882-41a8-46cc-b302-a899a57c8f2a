package com.frt.usercore.domain.result.operationadmin.menumanager;

import lombok.Data;

import java.util.List;

/**
 * 菜单列表查询结果
 *
 * <AUTHOR>
 * @version MenuListQueryResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class MenuListQueryResult {

    /**
     * 菜单ID
     */
    private String menuId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单类型 1-菜单 2-按钮
     */
    private Integer menuType;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 权限标识
     */
    private String permission;

    /**
     * 子菜单列表
     */
    private List<MenuListQueryResult> children;
}
