/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version RoleNameCheckParam.java, v 0.1 2025-08-30 17:27 zhangling
 */
@Data
public class RoleNameCheckParam implements Serializable {
    private static final long serialVersionUID = -8073639367671544902L;
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 平台类型
     */
    private Integer platformType;
}