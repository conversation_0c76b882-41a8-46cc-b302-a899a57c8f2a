package com.frt.usercore.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 系统配置类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
@Component
@RefreshScope
public class SysConfig {

    /**
     * 运营后台最大错误次数
     */
    @Value("${login.password.operation.max-error-count:5}")
    private Integer operationMaxErrorCount;

    /**
     * 商户最大错误次数
     */
    @Value("${login.password.merchant.max-error-count:5}")
    private Integer merchantMaxErrorCount;

    /**
     * 商户锁定时长（分钟）
     */
    @Value("${login.password.merchant.lock-time:5}")
    private Integer merchantLockTime;

    /**
     * 运营后台展示锁定时间（分钟）
     */
    @Value("${login.password.operation.show-lock-time:15}")
    private Integer operationShowLockTime;

    /**
     * 商户展示锁定时间（分钟）
     */
    @Value("${login.password.merchant.show-lock-time:5}")
    private Integer merchantShowLockTime;
}
