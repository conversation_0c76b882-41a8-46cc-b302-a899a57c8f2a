/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.MerchantUserAccountCheckResult;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.UserStoreListResult;
import com.frt.usercore.service.MerchantUserManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version UserManagerApi.java, v 0.1 2025-08-27 17:29 zhangling
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/user")
public class UserManagerApi {

    private final MerchantUserManagerService merchantUserManagerService;

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/query-user-list")
    PageResult<UserInfo> getUserList(@RequestBody PageParam<UserListQueryParam> param) {
        return merchantUserManagerService.getUserList(param);
    }

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @PostMapping("/get-user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param) {
        return merchantUserManagerService.getUserDetail(param);
    }

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/add-user")
    void addUser(@RequestBody UserAddParam param) {
        merchantUserManagerService.addUser(param);
    }

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/update-user")
    void updateUser(@RequestBody UserUpdateParam param) {
        merchantUserManagerService.updateUser(param);
    }

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/delete-user")
    void deleteUser(@RequestBody UserDeleteParam param) {
        merchantUserManagerService.deleteUser(param);
    }

    /**
     * 禁用/启用员工
     *
     * @param param 禁用/启用员工
     * @return 操作结果
     */
    @PostMapping("/disable-and-enable-user")
    void disableAndEnableUser(@RequestBody UserDisableAndEnableParam param) {
        merchantUserManagerService.disableAndEnableUser(param);
    }

    /**
     * 校验员工账号
     * @param param
     * @return
     */
    @PostMapping("/check-user-account")
    MerchantUserAccountCheckResult checkUserAccount(@RequestBody MerchantUserAccountCheckParam param) {
        return merchantUserManagerService.checkUserAccount(param);
    }

    @PostMapping("/update-password")
    void updatePassword(@RequestBody MerchantUserUpdatePasswordParam param) {
        merchantUserManagerService.updatePassword(param);
    }

    /**
     * 查询员工门店列表
     * @param param
     * @return
     */
    @PostMapping("/query-user-store-list")
    public UserStoreListResult queryUserStoreList(@RequestBody UserStoreListParam param) {
        return merchantUserManagerService.queryUserStoreList(param);
    }
}