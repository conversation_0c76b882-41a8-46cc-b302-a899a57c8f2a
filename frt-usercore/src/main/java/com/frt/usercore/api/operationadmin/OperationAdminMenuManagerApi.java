package com.frt.usercore.api.operationadmin;

import com.frt.usercore.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.usercore.domain.result.operationadmin.menumanager.MenuListQueryResult;
import com.frt.usercore.service.OperationAdminMenuManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运营后台菜单管理API
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerApi.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/operation/admin/menu")
public class OperationAdminMenuManagerApi {

    private final OperationAdminMenuManagerService operationAdminMenuManagerService;

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @PostMapping("/menu-list")
    public List<MenuListQueryResult> getMenuList(@RequestBody MenuListQueryParam param) {
        return operationAdminMenuManagerService.getMenuList(param);
    }
}
