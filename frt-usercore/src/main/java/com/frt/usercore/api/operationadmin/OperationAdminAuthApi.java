/**
 * <AUTHOR>
 * @date 2025/8/27 14:45
 * @version 1.0 OperationAdminController
 */
package com.frt.usercore.api.operationadmin;

import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminChangePasswordParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminCheckCodeParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSearchPhoneParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSendCodeParam;
import com.frt.usercore.domain.param.operationadmin.auth.QueryAccountParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.common.SmsCodeCheckResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.usercore.domain.result.operationadmin.auth.QueryAccountResult;
import com.frt.usercore.service.OperationAdminAuthService;
import com.frt.usercore.service.common.SmsCommonService;
import com.frt.usercore.service.common.UserCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台权限接口
 */
@RestController
@RequestMapping("/operation/web")
public class OperationAdminAuthApi {

    @Autowired
    private OperationAdminAuthService operationAdminAuthService;
    @Autowired
    private UserCommonService userCommonService;
    @Autowired
    private SmsCommonService smsCommonService;


    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public OperationAdminResourceResult searchResource(@RequestBody OperationAdminResourceParam param) {
        return operationAdminAuthService.searchResource(param);
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @PostMapping("/send/code")
    public void sendCode(@RequestBody OperationAdminSendCodeParam param) {
    }

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @PostMapping("/login")
    public OperationAdminLoginResult login(@RequestBody OperationAdminLoginParam param) {
        return operationAdminAuthService.login(param);
    }

    /**
     * 4.4 通过账号查询加密手机号
     * 接口名称：operation/web/search/phone
     * 请求方式：POST
     *
     * @param param 查询参数 account 账号
     * @return 手机号信息
     */
    @PostMapping("/search/phone")
    public String searchPhone(@RequestBody OperationAdminSearchPhoneParam param) {
        return operationAdminAuthService.searchPhone(param);
    }

    /**
     * 4.5 修改密码验证码校验
     * 接口名称：operation/web/check/code
     * 请求方式：POST
     *
     * @param param 验证参数
     *              code 验证码
     *              account 账号
     */
    @PostMapping("/check/code")
    public void checkCode(@RequestBody OperationAdminCheckCodeParam param) {
        operationAdminAuthService.checkCode(param);
    }

    /**
     * 4.6 设置新密码
     * 接口名称：operation/web/change/password
     * 请求方式：POST
     *
     * @param param 修改密码参数
     *              account 账号
     *              password 新密码
     *              secondPassword 新密码确认
     */
    @PostMapping("/change/password")
    public void changePassword(@RequestBody OperationAdminChangePasswordParam param) {
        operationAdminAuthService.changePassword(param);
    }

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @PostMapping("/logout")
    public void logout() {
        operationAdminAuthService.logout();
    }

    /**
     * 根据账号名称查询账号信息
     *
     * @param param 账号名称
     * @return 账号信息
     */
    @PostMapping("/query-account")
    public QueryAccountResult queryAccount(@RequestBody @Validated QueryAccountParam param) {
        param.setPlatformType(TenantContextUtil.getPlatformType().getCode());
        return userCommonService.queryAccount(param);
    }


    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/send-sms")
    public void sendSms(@RequestBody SendSmsParam param) {
        operationAdminAuthService.sendCode(param);
        // smsCommonService.sendSms(param.getPhone(), param.getSceneValue(), TenantContextUtil.getTenantId(), null);
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@RequestBody CheckSmsCodeParam param) {
        SmsCodeCheckResult result = smsCommonService.checkSmsCode(param.getPhone(), param.getSmsCode(), param.getSceneValue(), TenantContextUtil.getTenantId(), Boolean.FALSE);
        if (null == result || result.isFailure()) {
            throw ValidateUtil.validateMsg("验证码校验失败");
        }
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@RequestBody UpdatePasswordParam param) {
        operationAdminAuthService.updatePassword(param);
    }



}