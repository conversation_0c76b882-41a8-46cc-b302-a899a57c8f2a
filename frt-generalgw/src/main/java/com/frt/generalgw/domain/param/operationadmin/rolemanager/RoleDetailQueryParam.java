package com.frt.generalgw.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 角色详情查询参数
 *
 * <AUTHOR>
 * @version RoleDetailQueryParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class RoleDetailQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 访问令牌
     */
    @NotBlank(message = "访问令牌不能为空")
    private String accessToken;

    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;
}
