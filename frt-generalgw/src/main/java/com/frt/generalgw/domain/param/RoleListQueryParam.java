package com.frt.generalgw.domain.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色列表查询参数
 *
 * <AUTHOR>
 * @version RoleListQueryParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleListQueryParam implements Serializable {

    private static final long serialVersionUID = -481271297905358376L;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色模板id（角色类型）
     */
    private String roleTemplateId;
}