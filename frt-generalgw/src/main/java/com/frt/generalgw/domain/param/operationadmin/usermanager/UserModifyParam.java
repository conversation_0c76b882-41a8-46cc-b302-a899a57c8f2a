package com.frt.generalgw.domain.param.operationadmin.usermanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改员工参数
 *
 * <AUTHOR>
 * @version UserModifyParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class UserModifyParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 访问令牌
     */
    @NotBlank(message = "访问令牌不能为空")
    private String accessToken;

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

    /**
     * 员工账号
     */
    @NotBlank(message = "员工账号不能为空")
    private String username;

    /**
     * 密码（MD5）
     */
    private String password;

    /**
     * 员工姓名
     */
    @NotBlank(message = "员工姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 角色id
     */
    @NotBlank(message = "角色id不能为空")
    private String roleId;

    /**
     * 状态 1-正常 2-禁用 3-注销
     */
    @NotNull(message = "账号状态不能为空")
    private Integer accountStatus;
}
