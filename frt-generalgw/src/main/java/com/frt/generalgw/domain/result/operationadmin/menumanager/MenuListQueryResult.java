package com.frt.generalgw.domain.result.operationadmin.menumanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 权限列表查询结果
 *
 * <AUTHOR>
 * @version MenuListQueryResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class MenuListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menu_id;

    /**
     * 父菜单id
     */
    private String parentMenuId;

    /**
     * 菜单类型 1-页面 2-功能
     */
    private Integer menuType;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;
}
