/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolListQueryResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.common.ListResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaProtocolController
 *
 * <AUTHOR>
 * @version ProtocolController.java, v 0.1 2025-08-27 13:42 zhangling
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant/mina/protocol")
public class MerchantMinaProtocolController {

    private final ProtocolClient protocolClient;

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public ProtocolSignCheckResult getProtocolList(@RequestBody ProtocolListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(3);
        return protocolClient.findProtocolList(param);
    }

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-info")
    public ProtocolInfoResult getProtocolInfo(@RequestBody ProtocolQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(3);
        return protocolClient.getProtocolInfo(param);
    }
}