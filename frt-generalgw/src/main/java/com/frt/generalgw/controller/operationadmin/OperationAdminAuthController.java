/**
 * <AUTHOR>
 * @date 2025/8/27 14:45
 * @version 1.0 OperationAdminController
 */
package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.operationadmin.OperationAdminClient;
import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.generalgw.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.operationadmin.OperationAdminAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminAuthController
 *
 * <AUTHOR>
 * @version OperationAdminController.java, v 0.1 2025-08-27 14:45 tuyuwei
 */
@RestController
@RequestMapping("/operation/web/auth")
public class OperationAdminAuthController {

    @Autowired
    private OperationAdminClient operationAdminClient;



    @Autowired
    private OperationAdminAuthService operationAdminAuthService;

    @Autowired
    private ProtocolClient protocolClient;

    @Autowired
    private VerifyCodeService verifyCodeService;

    /**
     * 运营后台登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public OperationAdminResourceResult searchResource(@RequestBody OperationAdminResourceParam param) {
        return operationAdminAuthService.searchResource(param);
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     * @return 发送结果
     */
//    @PostMapping("/send/code")
//    public CommonResult<Void> sendCode(@RequestBody OperationAdminSendCodeParam param) {
//        operationAdminClient.sendCode(param);
//        return CommonResult.success();
//    }

    /**
     * 运营后台账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @PostMapping("/login")
    public OperationAdminLoginResult login(@RequestBody OperationAdminLoginParam param) {
        return operationAdminClient.login(param);
    }

    /**
     * 运营后台账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public void logout() {
        operationAdminClient.logout();
    }

    // ========== 忘记密码相关接口 ==========

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public VerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        return verifyCodeService.getVerifyCode();
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public OperationAdminCheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody OperationAdminCheckVerifyCodeParam param) {
        return operationAdminAuthService.checkVerifyCode(param);
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        operationAdminClient.sendSms(param);
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        operationAdminClient.checkSmsCode(param);
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@Validated @RequestBody UpdatePasswordParam param) {
        operationAdminClient.updatePassword(param);
    }

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public ProtocolSignCheckResult getProtocolList(@RequestBody ProtocolListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(1);
        return protocolClient.findProtocolList(param);
    }

    /**
     * 获取协议详情
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-info")
    public ProtocolInfoResult getProtocolInfo(@RequestBody ProtocolQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(1);
        return protocolClient.getProtocolInfo(param);
    }
}