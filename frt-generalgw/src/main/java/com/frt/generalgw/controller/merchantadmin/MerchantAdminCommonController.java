/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminCommonClient;
import com.frt.generalgw.domain.mapper.OssServiceMapper;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.param.oss.AliyunOssTokenParam;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonUnityCategoryListQueryResult;
import com.frt.generalgw.domain.result.oss.AliyunOssTokenResult;
import com.frt.generalgw.service.common.OssCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminCommonController
 */
@RestController
@RequestMapping("/merchant/web/common")
public class MerchantAdminCommonController {

    @Autowired
    private MerchantAdminCommonClient commonClient;
    @Autowired
    private OssCommonService ossCommonService;
    @Autowired
    private OssServiceMapper ossServiceMapper;

    /**
     * 获取ossToken
     *
     * @param param
     * @return
     */
    @PostMapping("/query/oss-token")
    public AliyunOssTokenResult getAliyunOssToken(@RequestBody AliyunOssTokenParam param) {
        return ossServiceMapper.toAliyunOssTokenResult(ossCommonService.getAliyunOssToken(param));
    }

    /**
     * 查询地址列表
     * @param param
     * @return
     */
    @PostMapping("/query/address-code-list")
    public CommonAddressCodeListQueryResult queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param) {
        return commonClient.queryAddressCodeList(param);
    }

    /**
     * 查询类目列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/unity-category-list")
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param) {
        return commonClient.queryUnityCategoryList(param);
    }
}