/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminStoreManagerClient;
import com.frt.generalgw.common.enums.PlatformSourceEnum;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreListQueryParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminStoreManagerController
 */
@RestController
@RequestMapping("/merchant/web/store")
public class MerchantAdminStoreManagerController {

    @Autowired
    private MerchantAdminStoreManagerClient merchantAdminStoreManagerClient;

    /**
     * 查询门店列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/list")
    public PageResult<MerchantAminStoreListQueryResult> queryStoreList(@RequestBody PageParam<MerchantAminStoreListQueryParam> param) {
        return merchantAdminStoreManagerClient.queryStoreList(param);
    }

    /**
     * 新增门店
     *
     * @param param
     * @return
     */
    @PostMapping("/add/info")
    public MerchantAminStoreInfoAddResult addStoreInfo(@RequestBody MerchantAminStoreInfoAddParam param) {
        param.setSource(PlatformSourceEnum.MERCHANT_ADMIN.getCode());
        return merchantAdminStoreManagerClient.addStoreInfo(param);
    }

    /**
     * 修改门店
     *
     * @param param
     * @return
     */
    @PostMapping("/update/info")
    public MerchantAminStoreInfoUpdateResult updateStoreInfo(@RequestBody MerchantAminStoreInfoUpdateParam param) {
        return merchantAdminStoreManagerClient.updateStoreInfo(param);
    }

    /**
     * 门店详情
     *
     * @param param
     * @return
     */
    @PostMapping("/query/info")
    public MerchantAminStoreInfoQueryResult queryStoreInfo(@RequestBody MerchantAminStoreInfoQueryParam param) {
        return merchantAdminStoreManagerClient.queryStoreInfo(param);
    }
}