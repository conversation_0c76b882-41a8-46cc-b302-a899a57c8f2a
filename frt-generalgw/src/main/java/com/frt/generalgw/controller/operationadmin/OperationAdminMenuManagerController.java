package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.operationadmin.OperationAdminMenuManagerClient;
import com.frt.generalgw.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.generalgw.domain.result.operationadmin.menumanager.MenuListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运营后台/OperationAdminMenuManagerController
 *
 * <AUTHOR>
 * @version MenuManagerController.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@RestController
@RequestMapping("/operation/web/menu")
public class OperationAdminMenuManagerController {

    @Autowired
    private OperationAdminMenuManagerClient operationAdminMenuManagerClient;

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @PostMapping("/menu-list")
    public List<MenuListQueryResult> getMenuList(@Validated @RequestBody MenuListQueryParam param) {
        return operationAdminMenuManagerClient.getMenuList(param);
    }
}
