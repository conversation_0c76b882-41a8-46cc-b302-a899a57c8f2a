/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.RoleManagerClient;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.entity.RoleInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.*;
import com.frt.generalgw.domain.result.common.ListResult;
import com.frt.generalgw.domain.result.common.LoginResult;
import com.frt.generalgw.domain.result.common.PageResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminRoleManagerController
 *
 * <AUTHOR>
 * @version MerchantAdminRoleManagerController.java, v 0.1 2025-08-27 14:52 zhangling
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant/web/role")
public class MerchantAdminRoleManagerController {

    private final RoleManagerClient roleManagerClient;

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/role-list")
    public PageResult<RoleInfoResult> getRoleList(@RequestBody PageParam<RoleListQueryParam> param) {
        return roleManagerClient.getRoleList(param);
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/role-detail")
    public RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        param.setUserId(this.getUserId());
        return roleManagerClient.getRoleDetail(param);
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-add")
    public void addRole(@RequestBody RoleAddParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        param.setUserId(this.getUserId());
        roleManagerClient.addRole(param);
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-update")
    public void updateRole(@RequestBody RoleUpdateParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        param.setUserId(this.getUserId());
        roleManagerClient.updateRole(param);
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-delete")
    public void deleteRole(@RequestBody RoleDeleteParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        param.setUserId(this.getUserId());
        roleManagerClient.deleteRole(param);
    }

    /**
     * 获取权限模版
     */
    @PostMapping("/get-permission-template")
    public MerchantCashierAndShopMenuPermissionResult getPermissionTemplate(@RequestBody MerchantMenuPermissionParam param) {
        param.setUserId(this.getUserId());
        return roleManagerClient.getPermissionTemplate(param);
    }

    /**
     * 获取权限菜单模板
     *
     * @param param
     * @return
     */
    @PostMapping("/role-template-list")
    public ListResult<RoleTemplateInfoResult> getRoleTemplateList(@RequestBody MerchantRoleTemplateParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        param.setUserId(this.getUserId());
        return roleManagerClient.getRoleTemplateList(param);
    }

    /**
     * 角色名称检查
     * @param param
     * @return
     */
    @PostMapping("/role-name-check")
    public RoleNameCheckResult checkRoleName(@RequestBody RoleNameCheckParam param) {
        param.setUserId(this.getUserId());
        return roleManagerClient.checkRoleName(param);
    }

    /**
     * 获取当前登录用户ID
     * @return
     */
    private String getUserId() {
        final LoginResult loginInfo =
                LoginContext.getLoginInfo();
        if (null == loginInfo || StringUtils.isBlank(loginInfo.getUserId())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "无 userId");
        }
        return loginInfo.getUserId();
    }
}