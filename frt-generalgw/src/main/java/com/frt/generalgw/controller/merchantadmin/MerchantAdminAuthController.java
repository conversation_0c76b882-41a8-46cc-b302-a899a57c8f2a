/**
 * <AUTHOR>
 * @date 2025/8/27 14:16
 * @version 1.0 MerchantAuthController
 */
package com.frt.generalgw.controller.merchantadmin;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAuthController.java, v 0.1 2025-08-27 14:16 tuyuwei
 */

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminClient;
import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.domain.mapper.MerchantAdminAuthControllerObjMapper;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminLoginParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.GetVerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.operationadmin.OperationAdminAuthService;
import com.frt.generalgw.service.merchantAdmin.MerchantAdminAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminAuthController
 */
@RestController
@RequestMapping("/merchant/web/auth")
public class MerchantAdminAuthController {

    @Autowired
    private MerchantAdminClient merchantAdminClient;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private OperationAdminAuthService operationAdminAuthService;
    @Autowired
    private MerchantAdminAuthControllerObjMapper mapper;

    @Autowired
    private MerchantAdminAuthService merchantAdminAuthService;

    @Autowired
    private ProtocolClient protocolClient;

    /**
     * 商户后台登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     * @Response {@link BaseResult<MerchantAdminResourceResult>}
     */
    @PostMapping("/search/resource")
    public MerchantAdminResourceResult searchResource(@RequestBody MerchantAdminResourceParam param) {
        MerchantAdminResourceResult result = merchantAdminAuthService.searchResource(param);
        return result;
    }

//    /**
//     * 3.1.2 发送验证码
//     *
//     * @param param 请求参数
//     * @return 发送结果
//     */
//    @PostMapping("/send/code")
//    public CommonResult<Void> sendCode(@RequestBody MerchantAdminSendCodeParam param) {
//        merchantAdminClient.sendCode(param);
//        return CommonResult.success();
//    }

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public MerchantAdminLoginResult login(@RequestBody MerchantAdminLoginParam param) {
        if (StringUtils.isNotBlank(param.getCode()) && 1 == param.getType()) {
            // todo: 验证码校验
        }
        return merchantAdminClient.login(param);
    }
//
//    /**
//     * 3.1.4 通过账号查询加密手机号
//     *
//     * @param param 查询参数
//     * @return 手机号信息
//     */
//    @PostMapping("/search/phone")
//    public CommonResult<MerchantAdminSearchPhoneResult> searchPhone(@RequestBody MerchantAdminSearchPhoneParam param) {
//        MerchantAdminSearchPhoneResult result = merchantAdminClient.searchPhone(param);
//        return CommonResult.success(result);
//    }
//
//    /**
//     * 3.1.5 修改密码验证码校验
//     *
//     * @param param 验证参数
//     * @return 校验结果
//     */
//    @PostMapping("/check/code")
//    public CommonResult<Void> checkCode(@RequestBody MerchantAdminCheckCodeParam param) {
//        merchantAdminClient.checkCode(param);
//        return CommonResult.success();
//    }
//
//    /**
//     * 3.1.6 设置新密码
//     *
//     * @param param 修改密码参数
//     * @return 修改结果
//     */
//    @PostMapping("/change/password")
//    public CommonResult<Void> changePassword(@RequestBody MerchantAdminChangePasswordParam param) {
//        merchantAdminClient.changePassword(param);
//        return CommonResult.success();
//    }

    /**
     * 3.1.7 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public void logout() {
        merchantAdminClient.logout();
    }

    // ========== 忘记密码相关接口 ==========


    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        return new GetVerifyCodeResult(verifyCodeService.getVerifyCode().getUrl());
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        // TODO: 临时注释，等待解决Lombok编译问题后恢复
        return mapper.toCheckVerifyCodeResult(operationAdminAuthService.checkVerifyCode(mapper.toOperationAdminCheckVerifyCodeParam(param)));
        // return new CheckVerifyCodeResult();
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        merchantAdminClient.sendSms(param);
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        merchantAdminClient.checkSmsCode(param);
    }

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public ProtocolSignCheckResult getProtocolList(@RequestBody ProtocolListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(3);
        return protocolClient.findProtocolList(param);
    }

    /**
     * 获取协议详情
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-info")
    public ProtocolInfoResult getProtocolInfo(@RequestBody ProtocolQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(3);
        return protocolClient.getProtocolInfo(param);
    }
}