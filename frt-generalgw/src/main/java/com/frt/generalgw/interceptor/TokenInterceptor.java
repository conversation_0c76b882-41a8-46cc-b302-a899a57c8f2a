package com.frt.generalgw.interceptor;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.common.enums.exception.AuthErrorEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.result.common.LoginResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;



public class TokenInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取token
        String token = request.getHeader("Access-Token");
        String port = request.getHeader("port");

        // token校验
        if (SaFoxUtil.isNotEmpty(token)) {
            // 如果token有效，设置登录态
//            if ( StpUtil.getLoginIdByToken(token) != null ) {
//                // 获取登录信息
//                SaSession session = StpUtil.getTokenSessionByToken(token);
//                LoginResult loginInfo = new LoginResult();
//                if (session != null) {
//                    Object o = session.get(token);
//                    loginInfo = (LoginResult) o;
//                }
//                if (loginInfo != null) {
//                    // 设置登录上下文
//                    LoginContext.setLoginInfo(loginInfo);
//                    return true;
//                }
//            }
            if ("1".equals(port)) {
                LoginResult loginInfo = new LoginResult();
                loginInfo.setUserId("1234567");
                loginInfo.setTenantId("123");
                loginInfo.setIsAdmin(1);
                loginInfo.setPlatformType(PlatformEnum.OPERATION.getCode());
                loginInfo.setPlatformId("123");
                loginInfo.setLoginPort(1);
                LoginContext.setLoginInfo(loginInfo);
                return true;
            } else if ("3".equals(port)) {
                LoginResult loginInfo = new LoginResult();
                loginInfo.setUserId("123456");
                loginInfo.setTenantId("123");
                loginInfo.setIsAdmin(1);
                loginInfo.setPlatformType(PlatformEnum.MERCHANT.getCode());
                loginInfo.setPlatformId("41242");
                loginInfo.setLoginPort(3);
                LoginContext.setLoginInfo(loginInfo);
                return true;
            } else if ("4".equals(port)) {
                LoginResult loginInfo = new LoginResult();
                loginInfo.setUserId("123456");
                loginInfo.setTenantId("123");
                loginInfo.setIsAdmin(1);
                loginInfo.setPlatformType(PlatformEnum.MERCHANT.getCode());
                loginInfo.setPlatformId("41242");
                loginInfo.setLoginPort(4);
                LoginContext.setLoginInfo(loginInfo);
                return true;
            }
        }
        // token无效或不存在，返回未登录错误
        throw AuthErrorEnum.LOGIN_ERROR.exception();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除登录上下文
        LoginContext.clear();
    }
}
