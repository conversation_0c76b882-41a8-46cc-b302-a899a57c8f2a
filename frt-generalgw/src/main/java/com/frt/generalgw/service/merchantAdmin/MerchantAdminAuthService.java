/**
 * <AUTHOR>
 * @date 2025/8/31 20:20
 * @version 1.0 MerchantAdminAuthService
 */
package com.frt.generalgw.service.merchantAdmin;

import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAdminAuthService.java, v 0.1 2025-08-31 20:20 tuyuwei
 */
public interface MerchantAdminAuthService {

    /**
     * 获取商户后台资源
     *
     * @param param
     * @return
     */
    MerchantAdminResourceResult searchResource(MerchantAdminResourceParam param);
}