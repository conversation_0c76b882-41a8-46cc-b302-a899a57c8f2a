package com.frt.generalgw.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import cn.hutool.core.util.ObjectUtil;
import com.frt.generalgw.common.enums.exception.AuthErrorEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminLoginResult;

public class TokenUtil {
    
    /**
     * 获取当前登录态（无需入参，从上下文获取token）
     * @return 登录用户信息
     */
    public static Object getLoginInfo() {
        // 从上下文获取token
        String token = LoginContext.getToken();
        if (SaFoxUtil.isEmpty(token)) {
            return null;
        }
        
        try {
            // 验证token是否有效
            if ( StpUtil.getLoginIdByToken(token) != null ) {
                return StpUtil.getLoginIdByToken(token);
            }
        } catch (Exception e) {
            // token无效或过期
            return null;
        }
        
        return null;
    }

    /**
     * 获取商户后台登录态
     * @return
     */
    public static MerchantAdminLoginResult getMerchantAdminInfo() {
        Object loginInfo = getLoginInfo();
        if (ObjectUtil.isNotNull(loginInfo)) {
            return (MerchantAdminLoginResult) loginInfo;
        }
        throw AuthErrorEnum.LOGIN_ERROR.exception();
    }

    /**
     * 获取商户小程序登录态
     * @return
     */
    public static MerchantMinaLoginResult getMerchantMinaInfo() {
        Object loginInfo = getLoginInfo();
        if (ObjectUtil.isNotNull(loginInfo)) {
            return (MerchantMinaLoginResult) loginInfo;
        }
        return null;
    }

    /**
     * 获取运营后台登录态
     * @return
     */
    public static OperationAdminLoginResult getOperationAdminInfo() {
        Object loginInfo = getLoginInfo();
        if (ObjectUtil.isNotNull(loginInfo)) {
            return (OperationAdminLoginResult) loginInfo;
        }
        return null;
    }

}
